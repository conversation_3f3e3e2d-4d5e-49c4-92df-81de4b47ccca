import os
import subprocess
import winreg
import time
import tkinter.messagebox as messagebox


def process_exists(process_name):
    """检查进程是否存在"""
    print(f"[检查进程] 正在检查进程: {process_name}")
    try:
        # 使用tasklist命令检查进程
        result = subprocess.run(
            ['tasklist', '/FI', f'IMAGENAME eq {process_name}'],
            capture_output=True,
            text=True,
            creationflags=subprocess.CREATE_NO_WINDOW
        )
        exists = process_name.lower() in result.stdout.lower()
        print(f"[检查进程] 进程 {process_name} {'存在' if exists else '不存在'}")
        if exists:
            print(f"[检查进程] tasklist输出: {result.stdout.strip()}")
        return exists
    except Exception as e:
        print(f"[检查进程] 检查进程时出错: {e}")
        return False


def get_process_path(process_name):
    """获取进程的程序路径"""
    print(f"[获取路径] 正在获取进程路径: {process_name}")
    try:
        # 使用wmic命令获取进程路径
        result = subprocess.run(
            ['wmic', 'process', 'where', f'name="{process_name}"', 'get', 'ExecutablePath', '/value'],
            capture_output=True,
            text=True,
            creationflags=subprocess.CREATE_NO_WINDOW
        )
        print(f"[获取路径] wmic命令返回码: {result.returncode}")
        for line in result.stdout.split('\n'):
            if 'ExecutablePath=' in line and line.strip() != 'ExecutablePath=':
                path = line.split('=', 1)[1].strip()
                print(f"[获取路径] 找到进程路径: {path}")
                return path
        print(f"[获取路径] 未找到进程 {process_name} 的路径")
    except Exception as e:
        print(f"[获取路径] 获取进程路径时出错: {e}")
    return ""


def kill_process(process_name):
    """结束进程"""
    print(f"[结束进程] 正在结束进程: {process_name}")
    try:
        result = subprocess.run(
            ['taskkill', '/F', '/IM', process_name],
            capture_output=True,
            text=True,
            creationflags=subprocess.CREATE_NO_WINDOW
        )
        success = result.returncode == 0
        if success:
            print(f"[结束进程] 成功结束进程: {process_name}")
        else:
            print(f"[结束进程] 结束进程失败: {process_name}")
            print(f"[结束进程] 错误信息: {result.stderr}")
        return success
    except Exception as e:
        print(f"[结束进程] 结束进程时出错: {e}")
        return False


def get_registry_value(hkey, subkey, value_name):
    """读取注册表值"""
    print(f"[注册表读取] 正在读取: {subkey}\\{value_name}")
    try:
        with winreg.OpenKey(hkey, subkey) as key:
            value, _ = winreg.QueryValueEx(key, value_name)
            print(f"[注册表读取] 成功读取值: {value}")
            return value
    except Exception as e:
        print(f"[注册表读取] 读取失败: {e}")
        return ""


def set_registry_value(hkey, subkey, value_name, value):
    """写入注册表值"""
    print(f"[注册表写入] 正在写入: {subkey}\\{value_name} = {value}")
    try:
        with winreg.OpenKey(hkey, subkey, 0, winreg.KEY_SET_VALUE) as key:
            winreg.SetValueEx(key, value_name, 0, winreg.REG_SZ, value)
        print(f"[注册表写入] 成功写入注册表")
        return True
    except Exception as e:
        print(f"[注册表写入] 写入失败: {e}")
        return False


def run_program(program_path, wait=False):
    """运行程序"""
    print(f"[运行程序] 正在启动: {program_path}")
    print(f"[运行程序] 等待模式: {'是' if wait else '否'}")
    try:
        if wait:
            result = subprocess.run(program_path, shell=True)
            print(f"[运行程序] 程序执行完成，返回码: {result.returncode}")
        else:
            process = subprocess.Popen(program_path, shell=True)
            print(f"[运行程序] 程序已启动，进程ID: {process.pid}")
        return True
    except Exception as e:
        print(f"[运行程序] 启动程序失败: {e}")
        return False


def get_steam_path():
    """获取Steam路径"""
    print("[获取Steam路径] 开始获取Steam安装路径")

    # 首先检查Steam进程是否存在
    print("[获取Steam路径] 方法1: 从运行中的Steam进程获取路径")
    if process_exists("Steam.exe"):
        steam_exe_path = get_process_path("Steam.exe")
        if steam_exe_path:
            steam_path = steam_exe_path.replace("\\Steam.exe", "")
            print(f"[获取Steam路径] 从进程获取到路径: {steam_path}")
            return steam_path

    # 从注册表获取Steam路径
    print("[获取Steam路径] 方法2: 从注册表获取路径")
    steam_path = get_registry_value(
        winreg.HKEY_CURRENT_USER,
        "SOFTWARE\\Valve\\Steam",
        "SteamPath"
    )
    if steam_path:
        steam_path = steam_path.replace("/", "\\")
        print(f"[获取Steam路径] 从注册表获取到路径: {steam_path}")
        return steam_path

    print("[获取Steam路径] 未能获取到Steam路径")
    return ""


def steam_auto_login(username, password):
    """Steam自动登录主函数"""
    print("=" * 50)
    print("[主函数] Steam自动登录开始")
    print(f"[主函数] 目标用户名: {username}")
    print(f"[主函数] 密码长度: {len(password)} 字符")
    print("=" * 50)

    steam_path = get_steam_path()

    if not steam_path:
        print("[主函数] 错误: 无法找到Steam路径！")
        messagebox.showerror("错误", "无法找到Steam路径！")
        return

    steam_exe = os.path.join(steam_path, "steam.exe")
    print(f"[主函数] Steam可执行文件路径: {steam_exe}")
    print(f"[主函数] Steam.exe文件存在: {os.path.exists(steam_exe)}")

    # 检查Steam进程是否存在
    if process_exists("Steam.exe"):
        print("[主函数] Steam正在运行，需要先结束进程")

        # 结束Steam进程
        if not kill_process("Steam.exe"):
            print("[主函数] 错误: Steam进程结束失败！")
            messagebox.showerror("提示", "Steam进程结束失败！")
            return

        # 等待进程完全结束
        print("[主函数] 等待2秒让进程完全结束...")
        time.sleep(2)

        # 验证进程是否真的结束了
        if process_exists("Steam.exe"):
            print("[主函数] 警告: Steam进程仍在运行")
        else:
            print("[主函数] Steam进程已成功结束")

        # 写入自动登录用户到注册表
        if not set_registry_value(
            winreg.HKEY_CURRENT_USER,
            "SOFTWARE\\Valve\\Steam",
            "AutoLoginUser",
            username
        ):
            print("[主函数] 错误: 注册表写入失败！")
            messagebox.showerror("提示", "注册表写入失败！")
            return

        # 启动Steam
        print("[主函数] 启动Steam（不带登录参数）")
        if not run_program(steam_exe):
            print("[主函数] 第一次启动失败，尝试带引号的路径")
            run_program(f'"{steam_exe}"')

    else:
        print("[主函数] Steam未运行，直接设置并启动")

        # Steam未运行，直接写入注册表并启动
        if not set_registry_value(
            winreg.HKEY_CURRENT_USER,
            "SOFTWARE\\Valve\\Steam",
            "AutoLoginUser",
            username
        ):
            print("[主函数] 错误: 注册表写入失败！")
            messagebox.showerror("提示", "注册表写入失败！")
            return

        # 使用登录参数启动Steam
        login_command = f'"{steam_exe}" -login {username} {password}'
        print(f"[主函数] 使用登录命令启动: {login_command}")
        if not run_program(login_command):
            print("[主函数] 带登录参数启动失败，尝试普通启动")
            run_program(f'"{steam_exe}"')

    print("[主函数] Steam自动登录流程完成")
    print("=" * 50)


def main():
    """主程序入口"""
    # 默认账号密码
    username = "hgepk48036"
    password = "PUpupu2928"
    
    print(f"开始Steam自动登录...")
    print(f"用户名: {username}")
    
    try:
        steam_auto_login(username, password)
        print("Steam自动登录程序执行完成")
    except Exception as e:
        print(f"执行过程中出现错误: {e}")
        messagebox.showerror("错误", f"执行过程中出现错误: {e}")


if __name__ == "__main__":
    main()
