import os
import subprocess
import winreg
import time
import tkinter.messagebox as messagebox


def process_exists(process_name):
    """检查进程是否存在"""
    try:
        # 使用tasklist命令检查进程
        result = subprocess.run(
            ['tasklist', '/FI', f'IMAGENAME eq {process_name}'],
            capture_output=True,
            text=True,
            creationflags=subprocess.CREATE_NO_WINDOW
        )
        return process_name.lower() in result.stdout.lower()
    except Exception:
        return False


def get_process_path(process_name):
    """获取进程的程序路径"""
    try:
        # 使用wmic命令获取进程路径
        result = subprocess.run(
            ['wmic', 'process', 'where', f'name="{process_name}"', 'get', 'ExecutablePath', '/value'],
            capture_output=True,
            text=True,
            creationflags=subprocess.CREATE_NO_WINDOW
        )
        for line in result.stdout.split('\n'):
            if 'ExecutablePath=' in line and line.strip() != 'ExecutablePath=':
                return line.split('=', 1)[1].strip()
    except Exception:
        pass
    return ""


def kill_process(process_name):
    """结束进程"""
    try:
        result = subprocess.run(
            ['taskkill', '/F', '/IM', process_name],
            capture_output=True,
            text=True,
            creationflags=subprocess.CREATE_NO_WINDOW
        )
        return result.returncode == 0
    except Exception:
        return False


def get_registry_value(hkey, subkey, value_name):
    """读取注册表值"""
    try:
        with winreg.OpenKey(hkey, subkey) as key:
            value, _ = winreg.QueryValueEx(key, value_name)
            return value
    except Exception:
        return ""


def set_registry_value(hkey, subkey, value_name, value):
    """写入注册表值"""
    try:
        with winreg.OpenKey(hkey, subkey, 0, winreg.KEY_SET_VALUE) as key:
            winreg.SetValueEx(key, value_name, 0, winreg.REG_SZ, value)
        return True
    except Exception:
        return False


def run_program(program_path, wait=False):
    """运行程序"""
    try:
        if wait:
            subprocess.run(program_path, shell=True)
        else:
            subprocess.Popen(program_path, shell=True)
        return True
    except Exception:
        return False


def get_steam_path():
    """获取Steam路径"""
    # 首先检查Steam进程是否存在
    if process_exists("Steam.exe"):
        steam_exe_path = get_process_path("Steam.exe")
        if steam_exe_path:
            return steam_exe_path.replace("\\Steam.exe", "")
    
    # 从注册表获取Steam路径
    steam_path = get_registry_value(
        winreg.HKEY_CURRENT_USER, 
        "SOFTWARE\\Valve\\Steam", 
        "SteamPath"
    )
    if steam_path:
        return steam_path.replace("/", "\\")
    
    return ""


def steam_auto_login(username, password):
    """Steam自动登录主函数"""
    steam_path = get_steam_path()
    
    if not steam_path:
        messagebox.showerror("错误", "无法找到Steam路径！")
        return
    
    steam_exe = os.path.join(steam_path, "steam.exe")
    
    # 检查Steam进程是否存在
    if process_exists("Steam.exe"):
        # 结束Steam进程
        if not kill_process("Steam.exe"):
            messagebox.showerror("提示", "Steam进程结束失败！")
            return
        
        # 等待进程完全结束
        time.sleep(2)
        
        # 写入自动登录用户到注册表
        if not set_registry_value(
            winreg.HKEY_CURRENT_USER,
            "SOFTWARE\\Valve\\Steam",
            "AutoLoginUser",
            username
        ):
            messagebox.showerror("提示", "注册表写入失败！")
            return
        
        # 启动Steam
        if not run_program(steam_exe):
            run_program(f'"{steam_exe}"')
    
    else:
        # Steam未运行，直接写入注册表并启动
        if not set_registry_value(
            winreg.HKEY_CURRENT_USER,
            "SOFTWARE\\Valve\\Steam",
            "AutoLoginUser",
            username
        ):
            messagebox.showerror("提示", "注册表写入失败！")
            return
        
        # 使用登录参数启动Steam
        login_command = f'"{steam_exe}" -login {username} {password}'
        if not run_program(login_command):
            run_program(f'"{steam_exe}"')


def main():
    """主程序入口"""
    # 默认账号密码
    username = "hgepk48036"
    password = "PUpupu2928"
    
    print(f"开始Steam自动登录...")
    print(f"用户名: {username}")
    
    try:
        steam_auto_login(username, password)
        print("Steam自动登录程序执行完成")
    except Exception as e:
        print(f"执行过程中出现错误: {e}")
        messagebox.showerror("错误", f"执行过程中出现错误: {e}")


if __name__ == "__main__":
    main()
